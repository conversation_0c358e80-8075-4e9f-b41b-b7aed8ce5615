<script lang="ts">
  import { onMount } from "svelte"
  import { derived } from "svelte/store"
  import { spring } from "svelte/motion"
  import { scrollY } from "$lib/stores/scroll"
  import {
    ChevronRight,
    Play,
    CheckCircle,
    Users,
    TrendingUp,
    Zap,
    Focus,
    BarChart3,
    Sparkles,
    ChevronDown,
    Brain,
  } from "lucide-svelte"
  import ContactModal from "$lib/components/ContactModal.svelte"
  import AgentDemo from "$lib/components/AgentDemo.svelte"
  import SEOAgentDemo from "$lib/components/SEOAgentDemo.svelte"
  import type { PageData } from "./$types"

  export let data: PageData

  const { homeContent, meta } = data
  const isScrolled = derived(scrollY, (y) => y > 50)

  const heroOpacity = spring(1)
  const heroScale = spring(1)

  let showContactModal = false

  $: {
    heroOpacity.set(1 - Math.min($scrollY, 300) / 300)
    heroScale.set(1 - (Math.min($scrollY, 300) / 300) * 0.2)
  }

  function openContactModal() {
    showContactModal = true
  }

  function handleModalSubmit(event: CustomEvent) {
    console.log("Contact form submitted:", event.detail)
    showContactModal = false
  }

  function scrollToSection(id: string) {
    document.getElementById(id)?.scrollIntoView({ behavior: "smooth" })
  }

  // Helper functions for agent styling
  function getAgentTagClass(index: number): string {
    const classes = ["linear-tag-green", "linear-tag-blue", "linear-tag-purple"]
    return classes[index % classes.length]
  }

  function getAgentTagLabel(agentName: string): string {
    // Extract a short label from the agent name
    if (
      agentName.toLowerCase().includes("content") ||
      agentName.toLowerCase().includes("seo")
    ) {
      return "Content Agent"
    }
    if (
      agentName.toLowerCase().includes("competitive") ||
      agentName.toLowerCase().includes("research")
    ) {
      return "Research Agent"
    }
    if (
      agentName.toLowerCase().includes("social") ||
      agentName.toLowerCase().includes("campaign") ||
      agentName.toLowerCase().includes("orchestrator")
    ) {
      return "Social Agent"
    }
    return "Agent"
  }

  onMount(() => {
    const handleScrollAnimations = () => {
      const elements = document.querySelectorAll(".linear-fade-in")
      elements.forEach((element) => {
        const elementTop = element.getBoundingClientRect().top
        const elementVisible = 150

        if (elementTop < window.innerHeight - elementVisible) {
          element.classList.add("visible")
        }
      })
    }

    window.addEventListener("scroll", handleScrollAnimations)
    handleScrollAnimations() // Initial check

    return () => {
      window.removeEventListener("scroll", handleScrollAnimations)
    }
  })
</script>

<svelte:head>
  <title>{meta.title}</title>
  <meta name="description" content={meta.description} />

  <!-- Open Graph meta tags for social media sharing -->
  <meta property="og:title" content={meta.title} />
  <meta property="og:description" content={meta.description} />
  <meta property="og:image" content="/og-preview.jpg" />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />
  <meta
    property="og:image:alt"
    content="Robynn AI - Marketing Agents for Startups"
  />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://robynn.ai" />

  <!-- Twitter Card meta tags -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content={meta.title} />
  <meta name="twitter:description" content={meta.description} />
  <meta name="twitter:image" content="/og-preview.jpg" />

  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap"
    rel="stylesheet"
  />
</svelte:head>

<div class="min-h-screen bg-background text-foreground">
  <!-- Navigation -->
  <nav class="linear-nav fixed top-0 left-0 right-0 z-50 px-6 py-4">
    <div class="max-w-7xl mx-auto flex items-center justify-between">
      <div class="flex items-center space-x-8">
        <div class="linear-heading text-xl font-bold text-foreground">
          Robynn.ai
        </div>
        <div class="hidden md:flex items-center space-x-6">
          <button
            on:click={() => scrollToSection("approach")}
            class="nav-link text-foreground font-medium transition-all"
          >
            Approach
          </button>
          <button
            on:click={() => scrollToSection("services")}
            class="nav-link text-foreground font-medium transition-all"
          >
            Services
          </button>
          <button
            on:click={() => scrollToSection("agents")}
            class="nav-link text-foreground font-medium transition-all"
          >
            Agents
          </button>
          <button
            on:click={() => scrollToSection("results")}
            class="nav-link text-foreground font-medium transition-all"
          >
            Results
          </button>
          <button
            on:click={() => scrollToSection("stories")}
            class="nav-link text-foreground font-medium transition-all"
          >
            Stories
          </button>
        </div>
      </div>
      <button
        on:click={openContactModal}
        class="linear-btn-primary px-6 py-2 rounded-lg"
      >
        ↗ Get Started
      </button>
    </div>
  </nav>

  <!-- Hero Section -->
  <section
    class="linear-hero min-h-screen flex items-center justify-center px-6 py-20"
  >
    <div class="max-w-7xl mx-auto text-center">
      <div class="linear-fade-in mb-8">
        {#if homeContent?.hero}
          <h1
            class="linear-heading text-5xl md:text-7xl font-bold mb-6 leading-tight"
          >
            {homeContent.hero.frontmatter.title}<span class="text-primary"
              >{homeContent.hero.frontmatter.titleHighlight}</span
            >
          </h1>
          <p
            class="linear-body text-xl md:text-2xl text-muted-foreground mb-8 max-w-4xl mx-auto"
          >
            {@html homeContent.hero.content}
          </p>
        {:else}
          <h1
            class="linear-heading text-5xl md:text-7xl font-bold mb-6 leading-tight"
          >
            The <span class="text-primary">10X fractional CMO</span><br />
            team you've been <span class="text-primary">looking for</span>
          </h1>
          <p
            class="linear-body text-xl md:text-2xl text-muted-foreground mb-8 max-w-4xl mx-auto"
          >
            Strategic 10x marketing team that scales with your ambitions. We
            combine seasoned CMO expertise with cutting-edge AI to transform
            your go-to-market strategy build your GTM machine.
          </p>
        {/if}
      </div>

      <div class="linear-fade-in mb-12">
        <div class="flex flex-wrap justify-center gap-3 mb-8">
          {#if homeContent?.hero?.frontmatter?.badge}
            <span class="linear-tag linear-tag-green"
              >{homeContent.hero.frontmatter.badge}</span
            >
          {:else}
            <span class="linear-tag linear-tag-green"
              >Strategic Partnership</span
            >
            <span class="linear-tag linear-tag-purple">AI-Powered</span>
          {/if}
        </div>

        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
          <button
            on:click={openContactModal}
            class="linear-btn-primary px-8 py-3 text-lg rounded-lg flex items-center justify-center"
          >
            {homeContent?.hero?.frontmatter?.ctaPrimary ||
              "Start Your Growth Story"}
            <ChevronRight class="ml-2 h-4 w-4" />
          </button>
          <a
            href="#agents"
            class="linear-btn-secondary px-8 py-3 text-lg rounded-lg inline-flex items-center justify-center"
          >
            See Agents In Action
          </a>
        </div>

        <p class="linear-mono text-sm text-muted-foreground">
          {homeContent?.hero?.frontmatter?.trustBadge ||
            "Trusted by 50+ scaling startups"}
        </p>
      </div>

      <!-- Offering Breakdown -->
      <div class="linear-fade-in">
        <div class="max-w-4xl mx-auto">
          <h3
            class="linear-heading text-2xl text-center mb-8 flex items-center justify-center gap-3"
          >
            <Sparkles class="h-6 w-6 text-primary" />
            Everything you need. Nothing you don't.
          </h3>

          <div class="grid md:grid-cols-3 gap-6 mb-6">
            <!-- Fractional CMO -->
            <div
              class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"
            >
              <div class="text-center">
                <div
                  class="inline-flex items-center px-4 py-2 bg-primary/10 text-primary rounded text-sm font-medium mb-4"
                >
                  Fractional CMO
                </div>
                <p class="linear-body text-muted-foreground">
                  Strategic clarity without full-time overhead
                </p>
              </div>
            </div>

            <!-- Expert Marketers -->
            <div
              class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"
            >
              <div class="text-center">
                <div
                  class="inline-flex items-center px-4 py-2 bg-blue-500/10 text-blue-600 rounded text-sm font-medium mb-4"
                >
                  Expert Marketers
                </div>
                <p class="linear-body text-muted-foreground">
                  Campaigns crafted by hands-on pros
                </p>
              </div>
            </div>

            <!-- AI Agents -->
            <div
              class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm"
            >
              <div class="text-center">
                <div
                  class="inline-flex items-center px-4 py-2 bg-purple-500/10 text-purple-600 rounded text-sm font-medium mb-4"
                >
                  AI Agents
                </div>
                <p class="linear-body text-muted-foreground">
                  Automations that move fast—and scale faster
                </p>
              </div>
            </div>
          </div>

          <!-- Subtle caption -->
          <div class="text-center">
            <p class="linear-mono text-sm text-muted-foreground/80">
              Custom-assembled for your company. No bloat. No fluff.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Section Separator -->
  <div class="linear-section-separator"></div>

  <!-- Problem Section -->
  <section class="py-20 px-6 linear-grid">
    <div class="max-w-7xl mx-auto">
      <div class="text-center mb-16 linear-fade-in">
        <h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">
          You've built something <span class="text-primary">remarkable</span>
        </h2>
        <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">
          You're past the early stage hustle. Your product works. Your customers
          love it. You've hit that magical 5M+ revenue milestone. But now you're
          facing a new challenge.
        </p>
      </div>

      <div class="grid md:grid-cols-3 gap-8">
        <div class="linear-card linear-fade-in p-6 rounded-lg">
          <Users class="h-8 w-8 text-primary mb-4" />
          <h3 class="linear-heading text-xl mb-4">Expensive Team Building</h3>
          <p class="linear-body text-muted-foreground">
            VP of Marketing + team costs $500K+ annually
          </p>
        </div>

        <div class="linear-card linear-fade-in p-6 rounded-lg">
          <Focus class="h-8 w-8 text-blue-400 mb-4" />
          <h3 class="linear-heading text-xl mb-4">Scattered Marketing</h3>
          <p class="linear-body text-muted-foreground">
            Tactics without strategy, campaigns without cohesion
          </p>
        </div>

        <div class="linear-card linear-fade-in p-6 rounded-lg">
          <BarChart3 class="h-8 w-8 text-orange-400 mb-4" />
          <h3 class="linear-heading text-xl mb-4">Unclear ROI</h3>
          <p class="linear-body text-muted-foreground">
            Spending money without knowing what's working
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- Dotted Line Separator -->
  <div class="linear-dotted-line"></div>

  <!-- Approach Section -->
  <section id="approach" class="py-20 px-6 bg-card/50">
    <div class="max-w-7xl mx-auto">
      <div class="text-center mb-16 linear-fade-in">
        <h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">
          Think different about marketing <br />
          <span class="text-primary">in the world of AI</span>
        </h2>
        <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">
          While others offer fractional hours, we offer strategic embedded team
          members and custom agents for your team. Our CMO-led team doesn't just
          provide strategy, we build your entire GTM machine from the tech stack
          to a GTM campaign system built for your needs.
        </p>
      </div>

      <div class="grid md:grid-cols-3 gap-8 mb-16">
        <div class="linear-card linear-fade-in p-6 rounded-lg">
          <div class="linear-mono text-4xl font-bold text-primary mb-4">01</div>
          <span class="linear-tag linear-tag-green mb-4 inline-block"
            >Embedded Team</span
          >
          <h3 class="linear-heading text-xl mb-4">
            Your marketing co-founder and your agents when and where you need
            them
          </h3>
          <p class="linear-body text-muted-foreground">
            We think like an owner, act like a partner, and deliver like the CMO
            you've been looking for.
          </p>
        </div>

        <div class="linear-card linear-fade-in p-6 rounded-lg">
          <div class="linear-mono text-4xl font-bold text-primary mb-4">02</div>
          <span class="linear-tag linear-tag-purple mb-4 inline-block"
            >AI-Native Approach</span
          >
          <h3 class="linear-heading text-xl mb-4">
            Built for efficiency, powered by intelligence. Automation for
            scaling up.
          </h3>
          <p class="linear-body text-muted-foreground">
            Our methodology leverages AI at every step, making us faster and
            more efficient than traditional approaches.
          </p>
        </div>

        <div class="linear-card linear-fade-in p-6 rounded-lg">
          <div class="linear-mono text-4xl font-bold text-primary mb-4">03</div>
          <span class="linear-tag linear-tag-green mb-4 inline-block"
            >Continuous Learning</span
          >
          <h3 class="linear-heading text-xl mb-4">
            End-to-end campaigns tried and tested to generate pipelines and
            revenue
          </h3>
          <p class="linear-body text-muted-foreground">
            Deep product understanding drives differentiated messaging that
            actually resonates with your market.
          </p>
        </div>
      </div>

      <div class="linear-card linear-fade-in p-8 rounded-lg">
        <h3 class="linear-heading text-2xl text-center mb-8">
          Strategic Leadership, Not Just Services
        </h3>
        <p class="linear-body text-center text-muted-foreground mb-8">
          You get the strategic thinking of a seasoned CMO, the execution power
          of a full marketing team, and the efficiency of AI-native tools. All
          without the politics, overhead, or six-figure salaries.
        </p>
        <div class="grid md:grid-cols-4 gap-6">
          <div class="text-center">
            <CheckCircle class="h-6 w-6 text-primary mx-auto mb-2" />
            <div class="linear-body font-semibold">Strategy</div>
            <div class="linear-body text-sm text-muted-foreground">
              GTM Strategy & Execution
            </div>
          </div>
          <div class="text-center">
            <CheckCircle class="h-6 w-6 text-primary mx-auto mb-2" />
            <div class="linear-body font-semibold">Messaging</div>
            <div class="linear-body text-sm text-muted-foreground">
              Differentiated Positioning
            </div>
          </div>
          <div class="text-center">
            <CheckCircle class="h-6 w-6 text-primary mx-auto mb-2" />
            <div class="linear-body font-semibold">Campaigns</div>
            <div class="linear-body text-sm text-muted-foreground">
              AI-Powered 1-to-1 Marketing
            </div>
          </div>
          <div class="text-center">
            <CheckCircle class="h-6 w-6 text-primary mx-auto mb-2" />
            <div class="linear-body font-semibold">Growth</div>
            <div class="linear-body text-sm text-muted-foreground">
              Predictable Pipeline
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <section id="services" class="py-20 px-6 linear-grid">
    <div class="max-w-7xl mx-auto">
      <div class="text-center mb-16 linear-fade-in">
        <h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">
          Our <span class="text-primary">Services</span>
        </h2>
        <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">
          We don't just advise—we build, deploy, and run the complete marketing
          infrastructure your growing business needs to scale efficiently.
        </p>
      </div>

      <div class="grid md:grid-cols-3 gap-8">
        <div class="linear-card linear-fade-in p-6 rounded-lg">
          <Brain class="h-8 w-8 text-primary mb-4" />
          <h3 class="linear-heading text-xl mb-4">Custom Marketing Agents</h3>
          <p class="linear-body text-muted-foreground">
            Build and run your custom marketing agents centered around your
            product, your audience and your business requirements.
          </p>
        </div>

        <div class="linear-card linear-fade-in p-6 rounded-lg">
          <Zap class="h-8 w-8 text-blue-400 mb-4" />
          <h3 class="linear-heading text-xl mb-4">AI Campaign Orchestration</h3>
          <p class="linear-body text-muted-foreground">
            Build and setup an entire AI based campaign orchestration machine
            for your business.
          </p>
        </div>

        <div class="linear-card linear-fade-in p-6 rounded-lg">
          <TrendingUp class="h-8 w-8 text-orange-400 mb-4" />
          <h3 class="linear-heading text-xl mb-4">
            Complete Marketing Tech Stack
          </h3>
          <p class="linear-body text-muted-foreground">
            Build, setup, and run your entire marketing tech stack from website
            to AI-powered automation, data pipelines and analytics dashboard.
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- Marketing Agents Section -->
  <section id="agents" class="py-20 px-6 bg-card/50">
    <div class="max-w-7xl mx-auto">
      <div class="text-center mb-16 linear-fade-in">
        <h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">
          Marketing <span class="text-primary">Agents</span>
        </h2>
        <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">
          Our AI Agents That Scale Your Marketing. Meet your new marketing team.
          Each agent is specifically designed to handle complex marketing tasks
          that typically require hours of manual work, delivering results in
          minutes with unprecedented accuracy and insight.
        </p>
      </div>

      <div class="grid md:grid-cols-3 gap-8">
        {#if homeContent?.marketingAgents && homeContent.marketingAgents.length > 0}
          {#each homeContent.marketingAgents as agent, index}
            <div class="linear-card linear-fade-in p-6 rounded-lg">
              <span
                class="linear-tag {getAgentTagClass(index)} mb-4 inline-block"
                >{getAgentTagLabel(agent.frontmatter.name)}</span
              >
              <h3 class="linear-heading text-xl mb-4">
                {agent.frontmatter.name}
              </h3>
              <p class="linear-body text-muted-foreground">
                {@html agent.content}
              </p>
            </div>
          {/each}
        {:else}
          <!-- Fallback content if dynamic loading fails -->
          <div class="linear-card linear-fade-in p-6 rounded-lg">
            <span class="linear-tag linear-tag-green mb-4 inline-block"
              >Content Agent</span
            >
            <h3 class="linear-heading text-xl mb-4">Custom Content Agent</h3>
            <p class="linear-body text-muted-foreground">
              Generate SEO optimized content in your brand voice. This agent
              analyzes your existing content, understands your unique voice and
              tone, then creates high-performing content that ranks while
              staying true to your brand identity.
            </p>
          </div>

          <div class="linear-card linear-fade-in p-6 rounded-lg">
            <span class="linear-tag linear-tag-blue mb-4 inline-block"
              >Research Agent</span
            >
            <h3 class="linear-heading text-xl mb-4">Competitive Researcher</h3>
            <p class="linear-body text-muted-foreground">
              Custom agent to do deep competitive analysis on real-time data.
              Continuously monitors your competitors' strategies, pricing,
              content, and market positioning to give you actionable insights
              and strategic advantages.
            </p>
          </div>

          <div class="linear-card linear-fade-in p-6 rounded-lg">
            <span class="linear-tag linear-tag-purple mb-4 inline-block"
              >Social Agent</span
            >
            <h3 class="linear-heading text-xl mb-4">Social Media Agent</h3>
            <p class="linear-body text-muted-foreground">
              Listen to your brand and your competitors' signals, assess
              sentiment and act upon them. This agent monitors social
              conversations, tracks brand mentions, analyzes sentiment trends,
              and provides real-time insights to optimize your social media
              strategy.
            </p>
          </div>
        {/if}
      </div>

      <!-- Live Agent Demo -->
      <div class="mt-16 max-w-4xl mx-auto linear-fade-in">
        <div class="text-center mb-8">
          <h3 class="linear-heading text-2xl font-bold mb-4">
            See Our Agents in Action
          </h3>
          <p class="linear-body text-muted-foreground">
            Try Athena, our competitive researcher, and see real-time analysis
            in action.
          </p>
        </div>
        <AgentDemo />
      </div>

      <!-- SEO Agent Demo -->
      <div class="max-w-3xl mx-auto mt-16">
        <div class="text-center mb-8">
          <h3 class="linear-heading text-3xl font-bold mb-4">
            🎯 SEO Keyword Research Demo
          </h3>
          <p class="linear-body text-muted-foreground">
            Try Lexi, our SEO strategist, and discover high-value keywords
            instantly.
          </p>
        </div>
        <SEOAgentDemo />
      </div>
    </div>
  </section>

  <!-- Results Section -->
  <section id="results" class="py-20 px-6 linear-grid">
    <div class="max-w-7xl mx-auto">
      <div class="text-center mb-16 linear-fade-in">
        <h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">
          Growth you can <span class="text-primary">measure</span>.<br />
          Impact you can <span class="text-primary">feel</span>.
        </h2>
        <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">
          Our clients typically see 40% improvement in marketing efficiency
          within 90 days, 60% reduction in customer acquisition costs within 6
          months, and 3x pipeline growth within the first year.
        </p>
      </div>

      <div class="grid md:grid-cols-4 gap-8">
        <div class="linear-chart text-center linear-fade-in">
          <div class="linear-metric">40%</div>
          <div class="linear-body text-muted-foreground">
            Marketing Efficiency
          </div>
          <div class="linear-mono text-sm text-muted-foreground mt-2">
            within 90 days
          </div>
        </div>

        <div class="linear-chart text-center linear-fade-in">
          <div class="linear-metric">60%</div>
          <div class="linear-body text-muted-foreground">CAC Reduction</div>
          <div class="linear-mono text-sm text-muted-foreground mt-2">
            within 6 months
          </div>
        </div>

        <div class="linear-chart text-center linear-fade-in">
          <div class="linear-metric">3x</div>
          <div class="linear-body text-muted-foreground">Pipeline Growth</div>
          <div class="linear-mono text-sm text-muted-foreground mt-2">
            within first year
          </div>
        </div>

        <div class="linear-chart text-center linear-fade-in">
          <div class="linear-metric">90%</div>
          <div class="linear-body text-muted-foreground">
            Client Satisfaction
          </div>
          <div class="linear-mono text-sm text-muted-foreground mt-2">
            measurable results
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Stories Section -->
  <section id="stories" class="py-20 px-6 bg-card/50">
    <div class="max-w-7xl mx-auto">
      <div class="text-center mb-16 linear-fade-in">
        <h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">
          Real companies. <span class="text-primary">Real results</span>.
        </h2>
        <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">
          See how we've helped AI and technology companies transform their
          go-to-market strategy and achieve remarkable growth.
        </p>
      </div>

      <div class="grid md:grid-cols-2 gap-8">
        <div class="linear-card linear-fade-in p-6 rounded-lg">
          <span class="linear-tag linear-tag-green mb-4 inline-block"
            >Protecto</span
          >
          <h3 class="linear-heading text-xl mb-4">
            Data Guardrails for Enterprise AI Agents
          </h3>
          <p class="linear-body text-muted-foreground mb-6">
            A brilliant AI privacy startup helping prevent data leaks, privacy
            violations, and compliance risks in AI automation couldn't break
            through in a crowded market. We repositioned them around "Data
            Guardrails" and built a comprehensive GTM strategy.
          </p>

          <div class="linear-testimonial mb-6">
            <p class="linear-body italic text-muted-foreground">
              "Working with Robynn was like having a strategic co-founder who
              understood both our technology and our market better than we did."
            </p>
            <div class="mt-4">
              <div class="linear-body font-semibold">Amar Kanagaraj</div>
              <div class="linear-mono text-sm text-muted-foreground">
                CEO, Protecto
              </div>
            </div>
          </div>

          <div class="grid grid-cols-3 gap-4 text-center">
            <div>
              <div class="linear-mono text-xl font-bold text-primary">300%</div>
              <div class="linear-body text-sm text-muted-foreground">
                Pipeline Growth
              </div>
            </div>
            <div>
              <div class="linear-mono text-xl font-bold text-primary">60%</div>
              <div class="linear-body text-sm text-muted-foreground">
                Shorter Cycles
              </div>
            </div>
            <div>
              <div class="linear-mono text-xl font-bold text-primary">3x</div>
              <div class="linear-body text-sm text-muted-foreground">
                Valuation
              </div>
            </div>
          </div>
        </div>

        <div class="linear-card linear-fade-in p-6 rounded-lg">
          <span class="linear-tag linear-tag-pink mb-4 inline-block"
            >Apptware</span
          >
          <h3 class="linear-heading text-xl mb-4">Design first AI Services</h3>
          <p class="linear-body text-muted-foreground mb-6">
            A successful AI services company was struggling to gain traction in
            the US market. We rebuilt their market entry strategy from the
            ground up, created campaigns and helped them achieve a 2X pipeline
            growth.
          </p>

          <div class="linear-testimonial mb-6">
            <p class="linear-body italic text-muted-foreground">
              "They transformed our US market entry from a costly experiment
              into our fastest-growing revenue stream."
            </p>
            <div class="mt-4">
              <div class="linear-body font-semibold">Harish Rohokale</div>
              <div class="linear-mono text-sm text-muted-foreground">
                CEO, Apptware
              </div>
            </div>
          </div>

          <div class="grid grid-cols-3 gap-4 text-center">
            <div>
              <div class="linear-mono text-xl font-bold text-primary">$8M</div>
              <div class="linear-body text-sm text-muted-foreground">
                Pipeline Built
              </div>
            </div>
            <div>
              <div class="linear-mono text-xl font-bold text-primary">150%</div>
              <div class="linear-body text-sm text-muted-foreground">
                Goal Exceeded
              </div>
            </div>
            <div>
              <div class="linear-mono text-xl font-bold text-primary">4</div>
              <div class="linear-body text-sm text-muted-foreground">
                Months
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Team Section -->
  <section class="py-20 px-6 linear-grid">
    <div class="max-w-7xl mx-auto">
      <div class="text-center mb-16 linear-fade-in">
        <h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">
          The Robynn <span class="text-primary">Team</span>
        </h2>
        <p class="linear-body text-xl text-muted-foreground max-w-4xl mx-auto">
          Meet the strategic minds behind your growth. We're not just
          marketers—we're growth architects with deep expertise in AI,
          technology, and scaling businesses.
        </p>
      </div>

      <div class="grid md:grid-cols-3 gap-8">
        <div class="linear-team-card linear-fade-in p-6">
          <div
            class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mb-4"
          >
            <span class="linear-mono text-xl font-bold text-primary">MK</span>
          </div>
          <h3 class="linear-heading text-xl mb-2">Madhukar Kumar</h3>
          <p class="linear-body text-muted-foreground mb-4">CEO, Co-Founder</p>
          <p class="linear-body text-muted-foreground mb-4">
            Former CMO at two unicorn startups. Two decades plus years scaling
            B2B SaaS companies from $5M to $100M+ ARR. Expert in product-led
            growth and AI-native marketing strategies including building
            compelling and memorable brands.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="linear-tag linear-tag-green"
              >Go-to-Market Strategy</span
            >
            <span class="linear-tag linear-tag-purple">Product-Led Growth</span>
          </div>
        </div>

        <div class="linear-team-card linear-fade-in p-6">
          <div
            class="w-16 h-16 bg-blue-400/20 rounded-full flex items-center justify-center mb-4"
          >
            <span class="linear-mono text-xl font-bold text-blue-400">JH</span>
          </div>
          <h3 class="linear-heading text-xl mb-2">Joel Horwitz</h3>
          <p class="linear-body text-muted-foreground mb-4">
            The AI Demand Gen Maestro
          </p>
          <p class="linear-body text-muted-foreground mb-4">
            Former Head of Growth at Series B AI company. Built marketing
            automation systems that scaled 10x without proportional team growth.
            Expert in AI Demand Gen Orchestration.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="linear-tag linear-tag-blue">Marketing Automation</span>
            <span class="linear-tag linear-tag-orange">Demand Generation</span>
          </div>
        </div>

        <div class="linear-team-card linear-fade-in p-6">
          <div
            class="w-16 h-16 bg-orange-400/20 rounded-full flex items-center justify-center mb-4"
          >
            <span class="linear-mono text-xl font-bold text-orange-400">MT</span
            >
          </div>
          <h3 class="linear-heading text-xl mb-2">Matt Tanner</h3>
          <p class="linear-body text-muted-foreground mb-4">
            SEO and Content 10Xer
          </p>
          <p class="linear-body text-muted-foreground mb-4">
            Software engineer turned SEO expert. Analyze and optimize content
            for AI-powered search engines. Helped large and small companies
            scale their organic traffic by orders of magnitude.
          </p>
          <div class="flex flex-wrap gap-2">
            <span class="linear-tag linear-tag-orange">AI Implementation</span>
            <span class="linear-tag linear-tag-purple">SEO</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-20 px-6 bg-card/50">
    <div class="max-w-4xl mx-auto text-center linear-fade-in">
      <h2 class="linear-heading text-4xl md:text-5xl font-bold mb-6">
        Your next chapter starts with a <span class="text-primary"
          >conversation</span
        >
      </h2>
      <p class="linear-body text-xl text-muted-foreground mb-12">
        We don't believe in hard sells or high-pressure tactics. We believe in
        finding the right fit. If you're ready to transform your marketing from
        a cost center into a growth engine, let's talk.
      </p>
      <div class="flex justify-center">
        <button
          on:click={openContactModal}
          class="linear-btn-primary px-8 py-3 text-lg rounded-lg flex items-center justify-center"
        >
          Start Your Growth Journey <ChevronRight class="ml-2 h-4 w-4" />
        </button>
      </div>
      <p class="linear-mono text-sm text-muted-foreground mt-8">
        Ready to grow smarter, not just faster?
      </p>
    </div>
  </section>
</div>

<!-- Contact Modal -->
<ContactModal
  bind:isOpen={showContactModal}
  on:close={() => (showContactModal = false)}
  on:submit={handleModalSubmit}
/>
