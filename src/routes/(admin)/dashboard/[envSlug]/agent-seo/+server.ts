import { json } from "@sveltejs/kit"
import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./$types"
import { seoStrategistAgent } from "$lib/agents/seo-strategist"
import { env } from "$env/dynamic/private"

// Keep the original POST handler for non-streaming requests
export const POST: RequestHandler = async ({ request, locals, url }) => {
  // Verify user is authenticated
  const { session } = locals.auth
  if (!session) {
    return json({ error: "Unauthorized" }, { status: 401 })
  }

  // Check if client wants streaming response
  const wantsStream = url.searchParams.get("stream") === "true"

  if (wantsStream) {
    return handleStreamingRequest(request)
  }

  try {
    const { message } = await request.json()

    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400 })
    }

    console.log(
      "SEO agent request received:",
      message.substring(0, 100) + "...",
    )

    // Generate response using the SEO strategist agent with timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(
        () =>
          reject(
            new Error("Agent timeout after 90 seconds - try a simpler query"),
          ),
        90000,
      )
    })

    const agentPromise = seoStrategistAgent.generate([
      {
        role: "user",
        content: message,
      },
    ])

    const response: any = await Promise.race([agentPromise, timeoutPromise])

    console.log(
      "SEO agent response generated, length:",
      response.text?.length || 0,
    )

    return json({
      response: response.text,
    })
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error"
    console.error("Error in SEO strategist agent:", errorMessage)

    if (errorMessage.includes("timeout")) {
      return json(
        {
          error:
            "Request timeout - the analysis is taking too long. Try a shorter, more specific query.",
          suggestion:
            'Example: "SEO keywords for fitness coaching" instead of long descriptions',
        },
        { status: 408 },
      )
    }

    if (
      errorMessage.includes("maximum context length") ||
      errorMessage.includes("context window")
    ) {
      return json(
        {
          error:
            "Too much data to process. The analysis found extensive keyword data but exceeded processing limits.",
          suggestion:
            "This indicates your domains have rich keyword data. Try analyzing fewer competitors at once.",
        },
        { status: 413 },
      )
    }

    return json(
      {
        error:
          "An error occurred while processing your request. Please try again with a simpler query.",
        details: errorMessage,
      },
      { status: 500 },
    )
  }
}

// New streaming handler
async function handleStreamingRequest(request: Request) {
  const { message } = await request.json()

  // Log streaming request start

  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder()

      // Helper to send SSE messages
      const send = (data: any) => {
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}\n\n`))
      }

      try {
        // Check if this is a gap analysis request
        const isGapAnalysis =
          message.toLowerCase().includes("analyze keyword gaps") ||
          message.toLowerCase().includes("gap analysis") ||
          message.toLowerCase().includes("competitors:")

        if (isGapAnalysis) {
          // Gap Analysis specific progress steps
          send({
            step: 1,
            action: "Validating domain formats and accessibility...",
            progress: 5,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 1500))

          send({
            step: 1,
            action: "Domains validated successfully",
            progress: 15,
            status: "completed",
          })

          // Step 2: Analyze Your Site
          send({
            step: 2,
            action: "Getting keywords for your domain...",
            progress: 20,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 2500))

          send({
            step: 2,
            action: "Found 150+ keywords for your site",
            progress: 35,
            status: "completed",
          })

          // Step 3: Analyze Competitors
          send({
            step: 3,
            action: "Analyzing competitor domains...",
            progress: 40,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 3000))

          send({
            step: 3,
            action: "Competitor analysis complete",
            progress: 55,
            status: "completed",
          })

          // Step 4: Find Intersections
          send({
            step: 4,
            action: "Comparing keyword rankings across domains...",
            progress: 60,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 2000))

          send({
            step: 4,
            action: "Found keyword overlaps and differences",
            progress: 75,
            status: "completed",
          })

          // Step 5: Calculate Gaps
          send({
            step: 5,
            action: "Identifying keyword opportunities and gaps...",
            progress: 80,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 2000))

          send({
            step: 5,
            action: "Calculated opportunity scores",
            progress: 90,
            status: "completed",
          })

          // Step 6: Generate Report
          send({
            step: 6,
            action: "Formatting your gap analysis report...",
            progress: 95,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 1000))

          send({
            step: 6,
            action: "Report generation complete",
            progress: 100,
            status: "completed",
          })
        } else {
          // Regular SEO analysis progress steps
          // Step 1: Industry Research
          send({
            step: 1,
            action: "Researching your industry and market trends...",
            progress: 10,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 2000))

          send({
            step: 1,
            action: "Industry research completed",
            progress: 20,
            status: "completed",
          })

          // Step 2: Keyword Discovery
          send({
            step: 2,
            action: "Discovering relevant keywords for your business...",
            progress: 30,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 3000))

          send({
            step: 2,
            action: "Found 30+ relevant keywords",
            progress: 40,
            status: "completed",
          })

          // Step 3: Volume Analysis
          send({
            step: 3,
            action: "Analyzing search volumes and trends...",
            progress: 50,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 2000))

          send({
            step: 3,
            action: "Search volume analysis complete",
            progress: 60,
            status: "completed",
          })

          // Step 4: Competition Analysis
          send({
            step: 4,
            action: "Checking keyword difficulty and competition...",
            progress: 70,
            status: "active",
          })

          await new Promise((resolve) => setTimeout(resolve, 2000))

          send({
            step: 4,
            action: "Competition analysis complete",
            progress: 80,
            status: "completed",
          })

          // Step 5: Report Generation
          send({
            step: 5,
            action: "Generating your SEO strategy report...",
            progress: 90,
            status: "active",
          })
        }

        // Final agent call
        console.log("Starting AI agent generation...")

        let response: any
        try {
          // Use optimized timeout for gap analysis with reduced data
          const timeoutDuration = isGapAnalysis ? 90000 : 120000 // 90s for gap analysis, 120s for others

          response = await Promise.race([
            seoStrategistAgent.generate([
              {
                role: "user",
                content: message,
              },
            ]),
            new Promise((_, reject) =>
              setTimeout(
                () =>
                  reject(
                    new Error(
                      `Agent generation timeout after ${timeoutDuration / 1000} seconds`,
                    ),
                  ),
                timeoutDuration,
              ),
            ),
          ])

          console.log("Agent generation completed")
        } catch (error) {
          console.error("Agent generation failed:", error)
          throw error
        }

        if (!isGapAnalysis) {
          send({
            step: 5,
            action: "Report generated successfully!",
            progress: 100,
            status: "completed",
          })
        }

        // Send the final response
        send({
          type: "final",
          response: response.text,
        })

        // Close the stream
        controller.close()
      } catch (error) {
        send({
          type: "error",
          error: error instanceof Error ? error.message : "Unknown error",
        })
        controller.close()
      }
    },
  })

  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    },
  })
}
